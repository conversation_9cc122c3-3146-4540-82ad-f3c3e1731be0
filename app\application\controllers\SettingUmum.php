<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Setting_Umum $settingumum
 * @property Pemerintahan_Model $pemerintahan
 * @property Master_Users $masterusers
 * @property CI_Upload $upload
 */
class SettingUmum extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Setting_Umum', 'settingumum');
        $this->load->model('Pemerintahan_Model', 'pemerintahan');
        $this->load->model('Master_Users', 'masterusers');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login');
        } else if (!isAdmin()) {
            return redirect('dashboard');
        }

        $where = array();
        if (getSessionValue('ROLE') == 'admin') {
            $where['a.id_user'] = getCurrentIdUser();
        }

        $setting_umum = $this->settingumum->getDefaultData($where);
        $pemerintahan = $this->pemerintahan->getDefaultData($where);

        $data = array();
        $data['title'] = 'Setting Umum';
        $data['content'] = 'settingumum';

        if ($setting_umum->num_rows() > 0) {
            $data['setting'] = $setting_umum->row();
        } else {
            $data['setting'] = null;
        }

        if ($pemerintahan->num_rows() > 0) {
            $data['pemerintahan'] = $pemerintahan->row();
        } else {
            $data['pemerintahan'] = null;
        }


        return $this->load->view('master', $data);
    }

    public function process_update()
    {
        if (!isPostAjax()) {
            return JSONResponseRequestReject();
        } else if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Session required');
        } else if (!isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $setting_umum = $this->settingumum->getDefaultData(array(
            'a.id_user' => getCurrentIdUser()
        ));

        $row = $setting_umum->row();

        $pemerintahan = $this->pemerintahan->getDefaultData(array(
            'id_user' => getCurrentIdUser()
        ));

        $row_pemerintahan = $pemerintahan->row();

        $currentuser = $this->masterusers->get(array('id' => getCurrentIdUser()))->row();

        $nama = getPost('nama');
        $files = $_FILES;
        $isi = getPost('isi');
        $desa = getPost('desa');
        $alamat = getPost('alamat');
        $kecamatan = getPost('kecamatan');
        $kabupaten = getPost('kabupaten');
        $provinsi = getPost('provinsi');
        $kodepos = getPost('kodepos');
        $email = getPost('email');
        $jam_kerja = getPost('jam_kerja');
        $kontak = getPost('kontak');
        $luas_tanah_kas = getPost('luas_tanah_kas');
        $luas_tanah_desa = getPost('luas_tanah_desa');
        $luas_dhkp = getPost('luas_dhkp');
        $visi = getPost('visi');
        $misi = getPost('misi');
        $facebook = getPost('facebook');
        $twitter = getPost('twitter');
        $youtube = getPost('youtube');
        $instagram = getPost('instagram');
        $formatsurat = getPost('formatsurat');
        $description = getPost('description');
        $keywords = getPost('keywords');
        $kodedesa = getPost('kodedesa');
        $slider_news = getPost('slider_news');

        $exec = array();
        $exec_pemerintahan = array();

        if (isset($files['foto']) && $files['foto']['size'] > 0) {
            try {
                $upload = doUpload_CloudStorage('foto', 'png|jpeg|jpg');
                $exec['foto'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        if (isset($files['logo_desa']) && $files['logo_desa']['size'] > 0) {
            try {
                $upload = doUpload_CloudStorage('logo_desa', 'png|jpeg|jpg');
                $exec['logo_desa'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        if (isset($_FILES['struktur']) && $_FILES['struktur']['size'] > 0) {
            try {
                $upload = doUpload_CloudStorage('struktur', 'png|jpeg|jpg');
                $exec['struktur'] = $upload['name'];
            } catch (Exception $ex) {
                return JSONResponseDefault('FAILED', $ex->getMessage());
            }
        }

        $exec['nama'] = $nama;
        $exec['isi'] = $isi;
        $exec['jabatan'] = $currentuser->admintype == 'Kelurahan' ? 'Lurah' : 'Kepala Desa';
        $exec['alamat'] = $alamat;
        $exec['desa'] = $desa;
        $exec['email'] = $email;
        $exec['jam_kerja'] = $jam_kerja;
        $exec['kontak'] = $kontak;
        $exec['kecamatan'] = $kecamatan;
        $exec['kabupaten'] = $kabupaten;
        $exec['provinsi'] = $provinsi;
        $exec['kodepos'] = $kodepos;
        $exec['luas_tanah_kas'] = $luas_tanah_kas ? $luas_tanah_kas : 0;
        $exec['luas_tanah_desa'] = $luas_tanah_desa ? $luas_tanah_desa : 0;
        $exec['luas_dhkp'] = $luas_dhkp ? $luas_dhkp : 0;
        $exec['id_user'] = getCurrentIdUser();
        $exec['facebook'] = $facebook;
        $exec['twitter'] = $twitter;
        $exec['youtube'] = $youtube;
        $exec['instagram'] = $instagram;
        $exec['format_surat'] = $formatsurat;
        $exec['kode_desa'] = $kodedesa;
        $exec['description'] = $description;
        $exec['keywords'] = $keywords;
        $exec['slider_news'] = $slider_news ? 1 : 0;

        $exec_pemerintahan['visi'] = $visi;
        $exec_pemerintahan['misi'] = $misi;
        $exec_pemerintahan['id_user'] = getCurrentIdUser();

        if ($setting_umum->num_rows() == 0) {
            $doExecute = $this->settingumum->insert($exec);
        } else {
            $doExecute = $this->settingumum->update(array(
                'id_user' => getCurrentIdUser()
            ), $exec);
        }

        if ($pemerintahan->num_rows() == 0) {
            $doExecutePemerintahan = $this->pemerintahan->insert($exec_pemerintahan);
        } else {
            $doExecutePemerintahan = $this->pemerintahan->update(array('id_user' => getCurrentIdUser()), $exec_pemerintahan);
        }

        if ($doExecute && $doExecutePemerintahan) {
            return JSONResponseDefault('OK', 'Settingan berhasil disimpan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menyimpan setting');
        }
    }
}
